// ignore_for_file: unused_import, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hemmaerp/models/customers.dart';
import 'package:hemmaerp/widgets/forms/custom_text_field.dart';
import 'package:hemmaerp/widgets/forms/form_grid.dart';

class HealthInfoScreen extends StatefulWidget {
  final Customer customer;
  final Function(Customer) onUpdate;

  const HealthInfoScreen({
    Key? key,
    required this.customer,
    required this.onUpdate,
  }) : super(key: key);

  @override
  HealthInfoScreenState createState() => HealthInfoScreenState();
}

class HealthInfoScreenState extends State<HealthInfoScreen> {
  late TextEditingController _healthConditionsController;
  Set<String> _selectedHealthConditions = {};

  // Comprehensive list of health conditions
  static const List<String> _commonHealthConditions = [
    'None',
    'Asthma',
    'Diabetes Type 1',
    'Diabetes Type 2',
    'Heart Condition',
    'High Blood Pressure',
    'Low Blood Pressure',
    'Allergies (Food)',
    'Allergies (Environmental)',
    'Allergies (Medication)',
    'Joint Problems',
    'Back Pain',
    'Neck Pain',
    'Knee Problems',
    'Ankle Problems',
    'Shoulder Problems',
    'Epilepsy',
    'Migraine',
    'Chronic Headaches',
    'Vision Problems',
    'Hearing Problems',
    'Respiratory Issues',
    'Chronic Fatigue',
    'Arthritis',
    'Osteoporosis',
    'Thyroid Disorder',
    'Kidney Disease',
    'Liver Disease',
    'Skin Conditions',
    'Mental Health Conditions',
    'Previous Injuries',
    'Recent Surgery',
    'Pregnancy',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _healthConditionsController = TextEditingController();
    _initializeSelectedConditions();
  }

  void _initializeSelectedConditions() {
    if (widget.customer.healthConditions.isNotEmpty) {
      final existingConditions =
          widget.customer.healthConditions
              .split(',')
              .map((c) => c.trim())
              .where((c) => c.isNotEmpty)
              .toSet();

      setState(() {
        _selectedHealthConditions = existingConditions;
        if (_selectedHealthConditions.isEmpty) {
          _selectedHealthConditions.add('None');
        }
      });

      // Set additional conditions in text field
      final additionalConditions = _selectedHealthConditions
          .where((condition) => !_commonHealthConditions.contains(condition))
          .join(', ');
      _healthConditionsController.text = additionalConditions;
    } else {
      setState(() {
        _selectedHealthConditions = {'None'};
      });
    }
  }

  @override
  void dispose() {
    _healthConditionsController.dispose();
    super.dispose();
  }

  void _updateHealthConditions() {
    final allConditions = <String>{};
    allConditions.addAll(_selectedHealthConditions);

    // Add custom conditions from text field
    if (_healthConditionsController.text.isNotEmpty) {
      final customConditions = _healthConditionsController.text
          .split(',')
          .map((c) => c.trim())
          .where((c) => c.isNotEmpty);
      allConditions.addAll(customConditions);
    }

    // Remove 'None' if other conditions are selected
    if (allConditions.length > 1) {
      allConditions.remove('None');
    }

    final healthConditionsString = allConditions.join(', ');
    widget.onUpdate(
      widget.customer.copyWith(healthConditions: healthConditionsString),
    );
  }

  void _toggleHealthCondition(String condition) {
    setState(() {
      if (condition == 'None') {
        if (_selectedHealthConditions.contains('None')) {
          _selectedHealthConditions.remove('None');
        } else {
          _selectedHealthConditions.clear();
          _selectedHealthConditions.add('None');
        }
      } else {
        _selectedHealthConditions.remove('None');
        if (_selectedHealthConditions.contains(condition)) {
          _selectedHealthConditions.remove(condition);
          if (_selectedHealthConditions.isEmpty) {
            _selectedHealthConditions.add('None');
          }
        } else {
          _selectedHealthConditions.add(condition);
        }
      }
    });
    _updateHealthConditions();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: FormCard(
        title: 'Health Information',
        subtitle: 'Select any health conditions that apply to you',
        icon: Icon(
          Icons.medical_services,
          color: theme.colorScheme.primary,
          size: 28,
        ),
        children: [
          // Health Conditions Section
          Text(
            'Common Health Conditions',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select any health conditions that apply:',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(height: 12),

          // Health Condition Chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                _commonHealthConditions.map((condition) {
                  final isSelected = _selectedHealthConditions.contains(
                    condition,
                  );
                  final isNone = condition == 'None';

                  return FilterChip(
                    label: Text(
                      condition,
                      style: TextStyle(
                        color:
                            isSelected
                                ? (isNone
                                    ? theme.colorScheme.onSecondary
                                    : theme.colorScheme.onPrimary)
                                : theme.colorScheme.onSurface,
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) => _toggleHealthCondition(condition),
                    backgroundColor: theme.colorScheme.surface,
                    selectedColor:
                        isNone
                            ? theme.colorScheme.secondary
                            : theme.colorScheme.primary,
                    checkmarkColor:
                        isNone
                            ? theme.colorScheme.onSecondary
                            : theme.colorScheme.onPrimary,
                    side: BorderSide(
                      color:
                          isSelected
                              ? (isNone
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.primary)
                              : theme.colorScheme.outline.withOpacity(0.5),
                    ),
                    elevation: isSelected ? 2 : 0,
                    pressElevation: 4,
                  );
                }).toList(),
          ),

          const SizedBox(height: 24),

          // Additional Health Conditions Text Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Additional Health Conditions',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _healthConditionsController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText:
                      'Enter any other health conditions, allergies, or medications not listed above',
                  prefixIcon: const Icon(Icons.add_circle_outline),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: theme.colorScheme.outline.withOpacity(0.5),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: theme.colorScheme.outline.withOpacity(0.5),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: theme.colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                onChanged: (value) => _updateHealthConditions(),
              ),
              const SizedBox(height: 8),
              Text(
                'Selected conditions: ${_selectedHealthConditions.join(', ')}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
