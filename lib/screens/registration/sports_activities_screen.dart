// ignore_for_file: deprecated_member_use, unused_local_variable

import 'package:flutter/material.dart';
import 'package:hemmaerp/models/customers.dart';
import '../../models/customer_sport.dart';
import '../../models/sport.dart';
import '../../widgets/controls/custom_switch.dart';

class SportsActivitiesScreen extends StatefulWidget {
  final Customer customer;
  final List<Sport> availableSports;
  final Function(Customer) onUpdate;

  const SportsActivitiesScreen({
    Key? key,
    required this.customer,
    required this.availableSports,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<SportsActivitiesScreen> createState() => _SportsActivitiesScreenState();
}

class _SportsActivitiesScreenState extends State<SportsActivitiesScreen> {
  // List of customer sports
  late List<CustomerSport> _customerSports;

  // Selected sport for adding
  Sport? _selectedSport;

  // Total fees
  double _totalFees = 0.0;
  double _uniformFees = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize customer sports - create an empty list since Customer doesn't have sports field
    _customerSports = [];

    // Calculate initial fees
    _calculateTotalFees();
  }

  // Calculate total fees including uniforms
  void _calculateTotalFees() {
    double sportsFees = 0.0;
    double uniformFees = 0.0;

    for (var sport in _customerSports) {
      // Add sport fees
      sportsFees += sport.fees;

      // Add uniform fees if needed
      if (sport.uniformIncluded == true) {
        final sportObj = widget.availableSports.firstWhere(
          (s) => s.id == sport.sportId,
          orElse:
              () => Sport(
                name: '',
                trainers: '',
                fees: 0.0,
                number_of_sessions: 0,
                training_time: '',
                training_days: '',
              ),
        );
        uniformFees += sport.uniformPrice ?? 0.0;
      }
    }

    setState(() {
      _totalFees = sportsFees;
      _uniformFees = uniformFees;
    });

    // Update customer with new fees
    final updatedCustomer = widget.customer.copyWith(
      // Cannot set sports as it's not a field in Customer
      // Just update the sportActivity field with the first sport name if available
      sportActivity:
          _customerSports.isNotEmpty ? _customerSports.first.sportName : '',
    );

    widget.onUpdate(updatedCustomer);
  }

  // Add a new sport
  void _addSport() {
    if (_selectedSport == null) return;

    // Check if sport is already added
    final existingSport = _customerSports.any(
      (s) => s.sportId == _selectedSport!.id,
    );
    if (existingSport) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('This sport is already added')),
      );
      return;
    }

    // Create new customer sport
    final newSport = CustomerSport(
      sportId: _selectedSport!.id ?? 0,
      sportName: _selectedSport!.name,
      sportLevel: 'Beginner', // Default value
      trainer: '', // Default value
      subscriptionType: '', // Default value
      subscriptionDuration: '', // Default value
      numberOfSessions: _selectedSport!.number_of_sessions,
      fees: _selectedSport!.fees,
      uniformIncluded: false,
      trainingTime: '', // Default value
      trainingDays: '', // Default value
    );

    setState(() {
      _customerSports.add(newSport);
      _selectedSport = null;
    });

    _calculateTotalFees();
  }

  // Remove a sport
  void _removeSport(int index) {
    setState(() {
      _customerSports.removeAt(index);
    });

    _calculateTotalFees();
  }

  // Update sport details
  void _updateSportDetails(int index, CustomerSport updatedSport) {
    setState(() {
      _customerSports[index] = updatedSport;
    });

    _calculateTotalFees();
  }

  // Check for time conflicts
  bool _hasTimeConflict(CustomerSport sport, String time) {
    // Skip checking the current sport
    for (var s in _customerSports) {
      if (s.sportId == sport.sportId) continue;

      // Check if training time conflicts
      if (s.trainingTime == time) {
        return true;
      }
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sports selection section
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sports Activities',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Add new sport
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<Sport>(
                          decoration: InputDecoration(
                            labelText: 'Select Sport',
                            labelStyle: TextStyle(
                              color: theme.colorScheme.onSurface,
                            ),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: theme.colorScheme.outline,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: theme.colorScheme.outline,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: theme.colorScheme.primary,
                                width: 2,
                              ),
                            ),
                          ),
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          dropdownColor: theme.colorScheme.surface,
                          value: _selectedSport,
                          items:
                              widget.availableSports.map((sport) {
                                return DropdownMenuItem<Sport>(
                                  value: sport,
                                  child: Text(
                                    sport.name,
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white
                                              : Colors.black,
                                    ),
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSport = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: _addSport,
                        child: const Text('Add Sport'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // List of selected sports
                  if (_customerSports.isEmpty)
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'No sports selected yet',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _customerSports.length,
                      itemBuilder: (context, index) {
                        final sport = _customerSports[index];
                        final sportObj = widget.availableSports.firstWhere(
                          (s) => s.id == sport.sportId,
                          orElse:
                              () => Sport(
                                name: '',
                                trainers: '',
                                fees: 0.0,
                                number_of_sessions: 0,
                                training_time: '',
                                training_days: '',
                              ),
                        );

                        return _buildSportCard(index, sport, sportObj);
                      },
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Fees summary
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fees Summary',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Sports fees
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Sports Fees:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '﷼${_totalFees.toStringAsFixed(2)} SAR',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const Divider(),

                  // Uniform fees
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Uniform Fees:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '﷼${_uniformFees.toStringAsFixed(2)} SAR',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const Divider(),

                  // Total fees
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Fees:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '﷼${(_totalFees + _uniformFees).toStringAsFixed(2)} SAR',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build sport card with details
  Widget _buildSportCard(int index, CustomerSport sport, Sport sportObj) {
    final theme = Theme.of(context);
    // Get available trainers for this sport - trainers is a String, not a List
    final List<dynamic> trainers =
        sportObj.trainers
            .split(',')
            .map((t) => {'id': t.trim(), 'name': t.trim()})
            .toList();

    // Get available subscription types for this sport - create a default list since it doesn't exist
    final List<String> subscriptionTypes = ['Monthly', 'Quarterly', 'Yearly'];

    // Get available training times for this sport - use training_time instead
    final List<String> trainingTimes =
        sportObj.training_time.isNotEmpty
            ? sportObj.training_time.split(',').map((t) => t.trim()).toList()
            : [];

    // Parse training times into day-time map
    final Map<String, List<String>> dayToTimes = {};
    for (var time in trainingTimes) {
      final parts = time.split(' ');
      if (parts.length >= 2) {
        final day = parts[0];
        final timeRange = parts.sublist(1).join(' ');
        dayToTimes.putIfAbsent(day, () => []).add(timeRange);
      }
    }

    final List<String> days = dayToTimes.keys.toList();
    String? selectedDay;
    String? selectedTime;

    // Parse current selection
    if (sport.trainingTime.isNotEmpty) {
      final parts = sport.trainingTime.split(' ');
      if (parts.length >= 2) {
        selectedDay = parts[0];
        selectedTime = parts.sublist(1).join(' ');
      }
    }

    // Default to first day if none selected
    selectedDay ??= days.isNotEmpty ? days[0] : null;
    final List<String> timesForDay =
        selectedDay != null ? (dayToTimes[selectedDay] ?? []) : [];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sport header with delete button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  sport.sportName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _removeSport(index),
                ),
              ],
            ),
            const Divider(),

            // Sport level
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Sport Level',
                labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              dropdownColor: theme.colorScheme.surface,
              value: sport.sportLevel,
              items: [
                DropdownMenuItem(
                  value: 'Beginner',
                  child: Text(
                    'Beginner',
                    style: TextStyle(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                    ),
                  ),
                ),
                DropdownMenuItem(
                  value: 'Intermediate',
                  child: Text(
                    'Intermediate',
                    style: TextStyle(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                    ),
                  ),
                ),
                DropdownMenuItem(
                  value: 'Advanced',
                  child: Text(
                    'Advanced',
                    style: TextStyle(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                    ),
                  ),
                ),
              ],
              onChanged: (value) {
                final updatedSport = CustomerSport(
                  id: sport.id,
                  customerId: sport.customerId,
                  sportId: sport.sportId,
                  sportName: sport.sportName,
                  sportLevel: value ?? 'Beginner',
                  trainer: sport.trainer,
                  subscriptionType: sport.subscriptionType,
                  subscriptionDuration: sport.subscriptionDuration,
                  numberOfSessions: sport.numberOfSessions,
                  fees: sport.fees,
                  uniformIncluded: sport.uniformIncluded,
                  uniformPrice: sport.uniformPrice,
                  trainingTime: sport.trainingTime,
                  trainingDays: sport.trainingDays,
                );
                _updateSportDetails(index, updatedSport);
              },
            ),
            const SizedBox(height: 16),

            // Trainer selection
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Select Trainer',
                labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              dropdownColor: theme.colorScheme.surface,
              value: null, // trainerId is not in CustomerSport
              items:
                  trainers.map((trainer) {
                    return DropdownMenuItem<String>(
                      value: trainer.id,
                      child: Text(
                        trainer.name ?? 'Unknown Trainer',
                        style: TextStyle(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                        ),
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                // Since trainerId is not in CustomerSport, we'll just update the trainer name
                final updatedSport = CustomerSport(
                  id: sport.id,
                  customerId: sport.customerId,
                  sportId: sport.sportId,
                  sportName: sport.sportName,
                  sportLevel: sport.sportLevel,
                  trainer: value ?? '',
                  subscriptionType: sport.subscriptionType,
                  subscriptionDuration: sport.subscriptionDuration,
                  numberOfSessions: sport.numberOfSessions,
                  fees: sport.fees,
                  uniformIncluded: sport.uniformIncluded,
                  uniformPrice: sport.uniformPrice,
                  trainingTime: sport.trainingTime,
                  trainingDays: sport.trainingDays,
                );
                _updateSportDetails(index, updatedSport);
              },
            ),
            const SizedBox(height: 16),

            // Subscription type
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Subscription Type',
                labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              dropdownColor: theme.colorScheme.surface,
              value: sport.subscriptionType,
              items:
                  subscriptionTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(
                        type,
                        style: TextStyle(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black,
                        ),
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                final updatedSport = CustomerSport(
                  id: sport.id,
                  customerId: sport.customerId,
                  sportId: sport.sportId,
                  sportName: sport.sportName,
                  sportLevel: sport.sportLevel,
                  trainer: sport.trainer,
                  subscriptionType: value ?? '',
                  subscriptionDuration: sport.subscriptionDuration,
                  numberOfSessions: sport.numberOfSessions,
                  fees: sport.fees,
                  uniformIncluded: sport.uniformIncluded,
                  uniformPrice: sport.uniformPrice,
                  trainingTime: sport.trainingTime,
                  trainingDays: sport.trainingDays,
                );
                _updateSportDetails(index, updatedSport);
              },
            ),
            const SizedBox(height: 16),

            // Training times
            Text(
              'Training Times:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),

            // Day selection dropdown
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Select Day',
                labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              dropdownColor: theme.colorScheme.surface,
              value: selectedDay,
              items:
                  days
                      .map(
                        (day) => DropdownMenuItem<String>(
                          value: day,
                          child: Text(
                            day,
                            style: TextStyle(
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                            ),
                          ),
                        ),
                      )
                      .toList(),
              onChanged: (day) {
                if (day == null) return;
                final updatedSport = sport.copyWith(trainingTime: '');
                _updateSportDetails(index, updatedSport);
              },
            ),
            const SizedBox(height: 12),

            // Time selection dropdown
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Select Time',
                labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              dropdownColor: theme.colorScheme.surface,
              value:
                  selectedTime != null && timesForDay.contains(selectedTime)
                      ? selectedTime
                      : null,
              items:
                  timesForDay
                      .map(
                        (time) => DropdownMenuItem<String>(
                          value: time,
                          child: Text(
                            time,
                            style: TextStyle(
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                            ),
                          ),
                        ),
                      )
                      .toList(),
              onChanged: (time) {
                if (time == null || selectedDay == null) return;
                final fullTime = '$selectedDay $time';

                final hasConflict = _hasTimeConflict(sport, fullTime);
                if (hasConflict) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Time conflict with another sport'),
                    ),
                  );
                  return;
                }

                final updatedSport = sport.copyWith(trainingTime: fullTime);
                _updateSportDetails(index, updatedSport);
              },
            ),

            if (trainingTimes.isEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  'No training times available for this sport',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Uniform selection
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Need Uniform (﷼${sport.uniformPrice?.toStringAsFixed(2) ?? '0.00'} SAR)',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                CustomSwitch(
                  value: sport.uniformIncluded ?? false,
                  onChanged: (value) {
                    final updatedSport = CustomerSport(
                      id: sport.id,
                      customerId: sport.customerId,
                      sportId: sport.sportId,
                      sportName: sport.sportName,
                      sportLevel: sport.sportLevel,
                      trainer: sport.trainer,
                      subscriptionType: sport.subscriptionType,
                      subscriptionDuration: sport.subscriptionDuration,
                      numberOfSessions: sport.numberOfSessions,
                      fees: sport.fees,
                      uniformIncluded: value,
                      uniformPrice: sport.uniformPrice,
                      trainingTime: sport.trainingTime,
                      trainingDays: sport.trainingDays,
                    );
                    _updateSportDetails(index, updatedSport);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
